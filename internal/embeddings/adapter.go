package embeddings

import (
	"brainHub/internal/consts"
	"brainHub/internal/model"
	"brainHub/internal/model/embedding"
	"context"
	"encoding/json"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
)

// LLMParamsAdapter LLMParams 適配器
// 負責將現有的 LLMParams 結構轉換為 EmbeddingConfig
// 保持與現有系統的完全向後兼容性
type LLMParamsAdapter struct{}

// NewLLMParamsAdapter 創建新的適配器實例
func NewLLMParamsAdapter() *LLMParamsAdapter {
	return &LLMParamsAdapter{}
}

// ConvertToEmbeddingConfig 將 LLMParams 轉換為 EmbeddingConfig
// 充分利用現有的 EmbeddingModel 字段，保持向後兼容
func (adapter *LLMParamsAdapter) ConvertToEmbeddingConfig(llmParams *model.LLMParams) (*embedding.EmbeddingConfig, error) {
	if llmParams == nil {
		return nil, gerror.New("LLMParams cannot be nil")
	}

	// 確定 embedding 提供商類型
	provider, err := adapter.determineEmbeddingProvider(llmParams)
	if err != nil {
		return nil, gerror.Wrap(err, "failed to determine embedding provider")
	}

	// 確定 embedding 模型
	embeddingModel := adapter.determineEmbeddingModel(llmParams)
	if embeddingModel == "" {
		return nil, gerror.New("embedding model not specified in LLMParams")
	}

	// 創建基礎配置
	config := &embedding.EmbeddingConfig{
		Provider:   provider,
		Model:      embeddingModel,
		APIKey:     llmParams.Token,
		APIVersion: llmParams.APIVersion,
	}

	// 根據提供商類型設置 BaseURL
	// 對於 VertexAI 回退情況，BaseURL 需要從 JSON 中提取
	if provider == consts.EmbeddingVertexAI {
		// VertexAI 回退情況，BaseURL 將在 configureVertexAIFallback 中設置
		config.BaseURL = "" // 先設為空，後續從 JSON 中提取
	} else {
		// 其他情況直接使用 BaseUrl
		config.BaseURL = llmParams.BaseUrl
	}

	// 根據提供商類型設置特定配置
	switch provider {
	case consts.EmbeddingAOAI:
		err = adapter.configureAzureOpenAI(config, llmParams)
	case consts.EmbeddingOpenAI:
		err = adapter.configureOpenAI(config, llmParams)
	case consts.EmbeddingVertexAI:
		// VertexAI embedding 尚未實現，回退到 AOAI 配置
		g.Log().Cat(consts.CatalogEmbedding).Warning(context.Background(),
			"VertexAI embedding not implemented, configuring fallback to AOAI embedding")
		err = adapter.configureVertexAIFallback(config, llmParams)
	default:
		return nil, gerror.Newf("unsupported embedding provider: %s", provider)
	}

	if err != nil {
		return nil, gerror.Wrap(err, "failed to configure embedding provider")
	}

	// 設置默認值
	adapter.setDefaultValues(config)

	return config, nil
}

// determineEmbeddingProvider 確定 embedding 提供商類型
func (adapter *LLMParamsAdapter) determineEmbeddingProvider(llmParams *model.LLMParams) (string, error) {
	// 根據 LLMType 確定提供商
	switch llmParams.LLMType {
	case consts.AOAI:
		return consts.EmbeddingAOAI, nil
	case "openai":
		return consts.EmbeddingOpenAI, nil
	case consts.VertexAIGemini, consts.VertexAIClaude:
		return consts.EmbeddingVertexAI, nil
	default:
		// 嘗試從 BaseURL 推斷
		if gstr.Contains(llmParams.BaseUrl, "openai.azure.com") {
			return consts.EmbeddingAOAI, nil
		}
		if gstr.Contains(llmParams.BaseUrl, "api.openai.com") {
			return consts.EmbeddingOpenAI, nil
		}
		if gstr.Contains(llmParams.BaseUrl, "googleapis.com") {
			return consts.EmbeddingVertexAI, nil
		}

		return "", gerror.Newf("cannot determine embedding provider from LLMType: %s", llmParams.LLMType)
	}
}

// determineEmbeddingModel 確定 embedding 模型
func (adapter *LLMParamsAdapter) determineEmbeddingModel(llmParams *model.LLMParams) string {
	// 優先使用 EmbeddingModel 字段
	if llmParams.EmbeddingModel != "" {
		return llmParams.EmbeddingModel
	}

	// 如果 EmbeddingModel 為空，根據 LLMType 設置默認值
	switch llmParams.LLMType {
	case consts.AOAI:
		// 優先使用 ModelId，如果為空則使用默認值
		if llmParams.ModelId != "" {
			return llmParams.ModelId
		}
		return "text-embedding-ada-002" // Azure OpenAI 默認模型
	case "openai":
		return "text-embedding-3-small" // OpenAI 默認模型
	case consts.VertexAIGemini, consts.VertexAIClaude:
		// VertexAI embedding 尚未實現，回退到 AOAI 默認模型
		g.Log().Cat(consts.CatalogEmbedding).Infof(context.Background(),
			"VertexAI embedding not implemented for LLMType %s, using AOAI default model", llmParams.LLMType)
		return "text-embedding-ada-002" // 回退到 Azure OpenAI 默認模型
	default:
		return consts.EmbeddingDefaultModel
	}
}

// configureAzureOpenAI 配置 Azure OpenAI
func (adapter *LLMParamsAdapter) configureAzureOpenAI(config *embedding.EmbeddingConfig, llmParams *model.LLMParams) error {
	// 驗證必需字段
	if config.BaseURL == "" {
		return gerror.New("BaseURL is required for Azure OpenAI")
	}
	if config.APIKey == "" {
		return gerror.New("API key is required for Azure OpenAI")
	}
	if config.APIVersion == "" {
		// 設置默認 API 版本
		config.APIVersion = "2023-05-15"
	}

	// 設置 Azure OpenAI 特定的默認值
	config.MaxBatchSize = 100
	config.Timeout = 30

	return nil
}

// configureOpenAI 配置 OpenAI
func (adapter *LLMParamsAdapter) configureOpenAI(config *embedding.EmbeddingConfig, llmParams *model.LLMParams) error {
	// 設置默認 BaseURL（如果未提供）
	if config.BaseURL == "" {
		config.BaseURL = "https://api.openai.com/v1"
	}

	if config.APIKey == "" {
		return gerror.New("API key is required for OpenAI")
	}

	// 設置 OpenAI 特定的默認值
	config.MaxBatchSize = 100
	config.Timeout = 30

	return nil
}

// configureVertexAI 配置 Vertex AI
func (adapter *LLMParamsAdapter) configureVertexAI(config *embedding.EmbeddingConfig, llmParams *model.LLMParams) error {
	if config.BaseURL == "" {
		return gerror.New("BaseURL is required for Vertex AI")
	}

	// Vertex AI 通常使用服務帳戶認證，不需要 API Key
	// 但可能需要其他配置信息

	// 設置 Vertex AI 特定的默認值
	config.MaxBatchSize = 50 // Vertex AI 批次限制較小
	config.Timeout = 60      // Vertex AI 可能需要更長時間

	return nil
}

// configureVertexAIFallback 配置 VertexAI 回退到 AOAI
// 當 VertexAI embedding 不可用時，使用 AOAI embedding 作為回退方案
// 從 base_url JSON 中提取 AOAI embedding 配置
func (adapter *LLMParamsAdapter) configureVertexAIFallback(config *embedding.EmbeddingConfig, llmParams *model.LLMParams) error {
	// 記錄回退信息
	g.Log().Cat(consts.CatalogEmbedding).Infof(context.Background(),
		"Configuring VertexAI embedding fallback to AOAI for LLMType: %s", llmParams.LLMType)

	// 從 base_url JSON 中提取 AOAI embedding 配置
	aoaiConfig, err := adapter.extractAOAIConfigFromBaseURL(llmParams.BaseUrl)
	if err != nil {
		return gerror.Wrap(err, "failed to extract AOAI config from base_url")
	}

	// 檢查是否有必要的 AOAI 配置信息
	if llmParams.EmbeddingModel == "" {
		// 設置默認的 embedding 模型
		config.Model = "text-embedding-ada-002"
		g.Log().Cat(consts.CatalogEmbedding).Warning(context.Background(),
			"No embedding model specified, using default: text-embedding-ada-002")
	}

	// 使用從 base_url 提取的 AOAI 配置
	if aoaiConfig.BaseURL != "" {
		config.BaseURL = aoaiConfig.BaseURL
	}
	if aoaiConfig.APIVersion != "" {
		config.APIVersion = aoaiConfig.APIVersion
	}

	// 檢查回退配置的完整性
	if config.BaseURL == "" || config.APIKey == "" {
		g.Log().Cat(consts.CatalogEmbedding).Errorf(context.Background(),
			"AOAI fallback configuration incomplete for LLMType %s. BaseURL: %s, APIKey: %s",
			llmParams.LLMType, config.BaseURL, config.APIKey)

		return gerror.New("AOAI fallback configuration incomplete. Please ensure your base_url contains:\n" +
			"{\n" +
			"  \"region\": \"your-region\",\n" +
			"  \"project_id\": \"your-project\",\n" +
			"  \"base_url\": \"https://your-aoai-endpoint.openai.azure.com/\",\n" +
			"  \"api_version\": \"2024-05-01-preview\"\n" +
			"}\n" +
			"And provide: embedding_model and token fields")
	}

	// 使用 AOAI 的配置邏輯
	return adapter.configureAzureOpenAI(config, llmParams)
}

// AOAIConfig AOAI 配置結構
type AOAIConfig struct {
	BaseURL    string `json:"base_url"`
	APIVersion string `json:"api_version"`
}

// extractAOAIConfigFromBaseURL 從 base_url JSON 字符串中提取 AOAI embedding 配置
func (adapter *LLMParamsAdapter) extractAOAIConfigFromBaseURL(baseURLStr string) (*AOAIConfig, error) {
	if baseURLStr == "" {
		return nil, gerror.New("base_url is empty")
	}

	// 解析 JSON 字符串
	var baseURLData map[string]interface{}
	err := gjson.DecodeTo(baseURLStr, &baseURLData)
	if err != nil {
		return nil, gerror.Wrap(err, "failed to parse base_url JSON")
	}

	// 提取 AOAI 配置
	aoaiConfig := &AOAIConfig{}

	if baseURL, exists := baseURLData["base_url"]; exists {
		if baseURLStr, ok := baseURL.(string); ok {
			aoaiConfig.BaseURL = baseURLStr
		}
	}

	if apiVersion, exists := baseURLData["api_version"]; exists {
		if apiVersionStr, ok := apiVersion.(string); ok {
			aoaiConfig.APIVersion = apiVersionStr
		}
	}

	g.Log().Cat(consts.CatalogEmbedding).Debugf(context.Background(),
		"Extracted AOAI config from base_url: BaseURL=%s, APIVersion=%s",
		aoaiConfig.BaseURL, aoaiConfig.APIVersion)

	return aoaiConfig, nil
}

// setDefaultValues 設置默認值
func (adapter *LLMParamsAdapter) setDefaultValues(config *embedding.EmbeddingConfig) {
	if config.MaxBatchSize <= 0 {
		config.MaxBatchSize = consts.EmbeddingDefaultBatchSize
	}

	if config.Timeout <= 0 {
		config.Timeout = consts.EmbeddingDefaultTimeout
	}

	if config.MaxRetries <= 0 {
		config.MaxRetries = consts.EmbeddingMaxRetryAttempts
	}

	if config.RetryDelay <= 0 {
		config.RetryDelay = consts.EmbeddingRetryDelaySecond
	}

	if config.Dimensions <= 0 {
		config.Dimensions = embedding.GetModelDimensions(config.Model)
	}
}

// CreateEmbeddingFromLLMParams 從 LLMParams 創建 embedding 實例
func (adapter *LLMParamsAdapter) CreateEmbeddingFromLLMParams(llmParams *model.LLMParams) (IEmbeddings, error) {
	// 轉換為 EmbeddingConfig
	config, err := adapter.ConvertToEmbeddingConfig(llmParams)
	if err != nil {
		return nil, gerror.Wrap(err, "failed to convert LLMParams to EmbeddingConfig")
	}

	// 創建 embedding 實例
	embeddingInstance, err := GetFactory().CreateEmbedding(config.Provider)
	if err != nil {
		return nil, gerror.Wrap(err, "failed to create embedding instance")
	}

	// 初始化實例
	ctx := context.Background()
	err = embeddingInstance.Initialize(ctx, config)
	if err != nil {
		return nil, gerror.Wrap(err, "failed to initialize embedding instance")
	}

	return embeddingInstance, nil
}

// ValidateLLMParamsForEmbedding 驗證 LLMParams 是否適用於 embedding
func (adapter *LLMParamsAdapter) ValidateLLMParamsForEmbedding(llmParams *model.LLMParams) error {
	if llmParams == nil {
		return gerror.New("LLMParams cannot be nil")
	}

	// 檢查是否有 embedding 模型配置
	if llmParams.EmbeddingModel == "" {
		g.Log().Cat(consts.CatalogEmbedding).Warningf(nil,
			"EmbeddingModel not specified in LLMParams, will use default model for LLMType: %s",
			llmParams.LLMType)
	}

	// 檢查基本配置
	if llmParams.LLMType == "" {
		return gerror.New("LLMType is required")
	}

	// 根據類型檢查特定配置
	switch llmParams.LLMType {
	case consts.AOAI:
		if llmParams.BaseUrl == "" {
			return gerror.New("BaseUrl is required for Azure OpenAI")
		}
		if llmParams.Token == "" {
			return gerror.New("Token is required for Azure OpenAI")
		}
	case "openai":
		if llmParams.Token == "" {
			return gerror.New("Token is required for OpenAI")
		}
	}

	return nil
}

// ExtractEmbeddingModelFromJSON 從 JSON 字符串中提取 embedding 模型配置
// 用於處理可能存儲在 LLMParams 其他字段中的 embedding 配置
func (adapter *LLMParamsAdapter) ExtractEmbeddingModelFromJSON(jsonStr string) (string, error) {
	if jsonStr == "" {
		return "", nil
	}

	var config map[string]interface{}
	err := json.Unmarshal([]byte(jsonStr), &config)
	if err != nil {
		return "", gerror.Wrap(err, "failed to parse JSON")
	}

	// 嘗試提取 embedding_model 字段
	if embeddingModel, exists := config["embedding_model"]; exists {
		return gconv.String(embeddingModel), nil
	}

	return "", nil
}

// 全局適配器實例
var globalAdapter *LLMParamsAdapter

// GetAdapter 獲取全局適配器實例
func GetAdapter() *LLMParamsAdapter {
	if globalAdapter == nil {
		globalAdapter = NewLLMParamsAdapter()
	}
	return globalAdapter
}

// CreateEmbeddingFromLLMParams 便捷函數：從 LLMParams 創建 embedding 實例
func CreateEmbeddingFromLLMParams(llmParams *model.LLMParams) (IEmbeddings, error) {
	return GetAdapter().CreateEmbeddingFromLLMParams(llmParams)
}

// ValidateLLMParamsForEmbedding 便捷函數：驗證 LLMParams
func ValidateLLMParamsForEmbedding(llmParams *model.LLMParams) error {
	return GetAdapter().ValidateLLMParamsForEmbedding(llmParams)
}
