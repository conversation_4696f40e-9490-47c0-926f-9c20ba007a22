package embeddings

import (
	"brainHub/internal/consts"
	"brainHub/internal/model"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestNewLLMParamsAdapter 測試創建適配器
func TestNewLLMParamsAdapter(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		adapter := NewLLMParamsAdapter()
		t.AssertNE(adapter, nil)
	})
}

// TestDetermineEmbeddingProvider 測試確定提供商
func TestDetermineEmbeddingProvider(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		adapter := NewLLMParamsAdapter()

		// 測試 Azure OpenAI
		llmParams := &model.LLMParams{
			LLMType: consts.AOAI,
		}
		provider, err := adapter.determineEmbeddingProvider(llmParams)
		t.AssertEQ(err, nil)
		t.Assert(provider, consts.EmbeddingAOAI)

		// 測試 OpenAI
		llmParams.LLMType = "openai"
		provider, err = adapter.determineEmbeddingProvider(llmParams)
		t.AssertEQ(err, nil)
		t.Assert(provider, consts.EmbeddingOpenAI)

		// 測試 Vertex AI Gemini
		llmParams.LLMType = consts.VertexAIGemini
		provider, err = adapter.determineEmbeddingProvider(llmParams)
		t.AssertEQ(err, nil)
		t.Assert(provider, consts.EmbeddingVertexAI)

		// 測試 Vertex AI Claude
		llmParams.LLMType = consts.VertexAIClaude
		provider, err = adapter.determineEmbeddingProvider(llmParams)
		t.AssertEQ(err, nil)
		t.Assert(provider, consts.EmbeddingVertexAI)

		// 測試從 BaseURL 推斷 Azure OpenAI
		llmParams.LLMType = "unknown"
		llmParams.BaseUrl = "https://test.openai.azure.com"
		provider, err = adapter.determineEmbeddingProvider(llmParams)
		t.AssertEQ(err, nil)
		t.Assert(provider, consts.EmbeddingAOAI)

		// 測試從 BaseURL 推斷 OpenAI
		llmParams.BaseUrl = "https://api.openai.com"
		provider, err = adapter.determineEmbeddingProvider(llmParams)
		t.AssertEQ(err, nil)
		t.Assert(provider, consts.EmbeddingOpenAI)

		// 測試從 BaseURL 推斷 Vertex AI
		llmParams.BaseUrl = "https://us-central1-aiplatform.googleapis.com"
		provider, err = adapter.determineEmbeddingProvider(llmParams)
		t.AssertEQ(err, nil)
		t.Assert(provider, consts.EmbeddingVertexAI)

		// 測試無法確定提供商
		llmParams.LLMType = "unknown"
		llmParams.BaseUrl = "https://unknown.com"
		_, err = adapter.determineEmbeddingProvider(llmParams)
		t.AssertNE(err, nil)
		t.AssertIN("cannot determine embedding provider", err.Error())
	})
}

// TestDetermineEmbeddingModel 測試確定模型
func TestDetermineEmbeddingModel(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		adapter := NewLLMParamsAdapter()

		// 測試使用 EmbeddingModel 字段
		llmParams := &model.LLMParams{
			EmbeddingModel: "custom-embedding-model",
			LLMType:        consts.AOAI,
		}
		model := adapter.determineEmbeddingModel(llmParams)
		t.Assert(model, "custom-embedding-model")

		// 測試 Azure OpenAI 默認模型
		llmParams.EmbeddingModel = ""
		llmParams.LLMType = consts.AOAI
		model = adapter.determineEmbeddingModel(llmParams)
		t.Assert(model, "text-embedding-ada-002")

		// 測試 OpenAI 默認模型
		llmParams.LLMType = "openai"
		model = adapter.determineEmbeddingModel(llmParams)
		t.Assert(model, "text-embedding-3-small")

		// 測試 Vertex AI 默認模型（回退到 AOAI）
		llmParams.LLMType = consts.VertexAIGemini
		model = adapter.determineEmbeddingModel(llmParams)
		t.Assert(model, "text-embedding-ada-002") // 回退到 AOAI 默認模型

		// 測試未知類型默認模型
		llmParams.LLMType = "unknown"
		model = adapter.determineEmbeddingModel(llmParams)
		t.Assert(model, consts.EmbeddingDefaultModel)
	})
}

// TestValidateLLMParamsForEmbedding 測試驗證 LLMParams
func TestValidateLLMParamsForEmbedding(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		adapter := NewLLMParamsAdapter()

		// 測試 nil 參數
		err := adapter.ValidateLLMParamsForEmbedding(nil)
		t.AssertNE(err, nil)
		t.AssertIN("LLMParams cannot be nil", err.Error())

		// 測試缺少 LLMType
		llmParams := &model.LLMParams{}
		err = adapter.ValidateLLMParamsForEmbedding(llmParams)
		t.AssertNE(err, nil)
		t.AssertIN("LLMType is required", err.Error())

		// 測試 Azure OpenAI 缺少必需字段
		llmParams.LLMType = consts.AOAI
		err = adapter.ValidateLLMParamsForEmbedding(llmParams)
		t.AssertNE(err, nil)
		t.AssertIN("BaseUrl is required", err.Error())

		llmParams.BaseUrl = "https://test.openai.azure.com"
		err = adapter.ValidateLLMParamsForEmbedding(llmParams)
		t.AssertNE(err, nil)
		t.AssertIN("Token is required", err.Error())

		// 測試完整的 Azure OpenAI 配置
		llmParams.Token = "test-token"
		err = adapter.ValidateLLMParamsForEmbedding(llmParams)
		t.AssertEQ(err, nil)

		// 測試 OpenAI 缺少 Token
		llmParams.LLMType = "openai"
		llmParams.Token = ""
		err = adapter.ValidateLLMParamsForEmbedding(llmParams)
		t.AssertNE(err, nil)
		t.AssertIN("Token is required", err.Error())

		// 測試完整的 OpenAI 配置
		llmParams.Token = "test-token"
		err = adapter.ValidateLLMParamsForEmbedding(llmParams)
		t.AssertEQ(err, nil)
	})
}

// TestExtractEmbeddingModelFromJSON 測試從 JSON 提取模型
func TestExtractEmbeddingModelFromJSON(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		adapter := NewLLMParamsAdapter()

		// 測試空字符串
		model, err := adapter.ExtractEmbeddingModelFromJSON("")
		t.AssertEQ(err, nil)
		t.Assert(model, "")

		// 測試有效 JSON
		jsonStr := `{"embedding_model": "text-embedding-ada-002"}`
		model, err = adapter.ExtractEmbeddingModelFromJSON(jsonStr)
		t.AssertEQ(err, nil)
		t.Assert(model, "text-embedding-ada-002")

		// 測試沒有 embedding_model 字段的 JSON
		jsonStr = `{"other_field": "value"}`
		model, err = adapter.ExtractEmbeddingModelFromJSON(jsonStr)
		t.AssertEQ(err, nil)
		t.Assert(model, "")

		// 測試無效 JSON
		jsonStr = `{invalid json}`
		_, err = adapter.ExtractEmbeddingModelFromJSON(jsonStr)
		t.AssertNE(err, nil)
		t.AssertIN("failed to parse JSON", err.Error())
	})
}

// TestGlobalAdapter 測試全局適配器
func TestGlobalAdapter(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 測試獲取全局適配器
		adapter1 := GetAdapter()
		adapter2 := GetAdapter()
		t.Assert(adapter1, adapter2) // 應該是同一個實例

		// 測試便捷函數
		llmParams := &model.LLMParams{
			LLMType:        consts.AOAI,
			BaseUrl:        "https://test.openai.azure.com",
			Token:          "test-token",
			EmbeddingModel: "text-embedding-ada-002",
		}

		// 測試驗證便捷函數
		err := ValidateLLMParamsForEmbedding(llmParams)
		t.AssertEQ(err, nil)

		// 測試創建便捷函數
		embeddingInstance, err := CreateEmbeddingFromLLMParams(llmParams)
		if err != nil {
			// 如果失敗，可能是因為網絡連接問題，這是正常的
			t.AssertIN("Failed to create", err.Error())
		} else {
			// 如果成功，驗證實例不為空
			t.AssertNE(embeddingInstance, nil)
		}
	})
}
